<template>
<section>
  <el-table :data="certTemplList" style="width: 100%" row-key="id" default-expand-all :tree-props="{children: 'children', hasChildren: 'hasChildren'}">

  </el-table>

</section>
</template>
<script>
import { getCertTemplList } from '@/api/system/baseInit'
export default {
  data() {
    return {
      certTemplList: []
    }
  },
  computed: {

  },
  created() {
    this.loadCertTemplList()
  },
  methods: {
    loadCertTemplList() {
      getCertTemplList().then(res => {
        this.certTemplList = res
      })
    }
  }
}
</script>
<style scoped>

</style>
