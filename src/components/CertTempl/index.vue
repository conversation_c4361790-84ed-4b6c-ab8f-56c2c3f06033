<template>
  <section class="cert-template-container">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-input
        v-model="searchName"
        placeholder="请输入模板名称搜索"
        prefix-icon="el-icon-search"
        style="width: 300px; margin-bottom: 20px;"
        @input="handleSearch"
        clearable
      />
    </div>

    <!-- 模板卡片列表 -->
    <div class="template-cards-container">
      <div v-if="certTemplList.length === 0" class="empty-state">
        <el-empty description="暂无模板数据" />
      </div>

      <div v-else class="cards-grid">
        <div
          v-for="template in certTemplList"
          :key="template.id"
          class="template-card"
        >
          <!-- 主模板卡片 -->
          <el-card class="parent-card" shadow="hover">
            <div class="card-header">
              <div class="template-info">
                <h3 class="template-name">{{ template.name }}</h3>
                <p class="template-code">编码: {{ template.code }}</p>
                <p class="template-app">应用: {{ template.appCode }}</p>
              </div>
              <div class="card-actions">
                <el-button type="primary" size="small" icon="el-icon-edit" @click="editTemplate(template)">
                  编辑
                </el-button>
                <el-button type="danger" size="small" icon="el-icon-delete" @click="deleteTemplate(template)">
                  删除
                </el-button>
              </div>
            </div>

            <div class="template-meta">
              <div class="meta-item">
                <span class="meta-label">创建时间:</span>
                <span class="meta-value">{{ template.createTime }}</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">更新时间:</span>
                <span class="meta-value">{{ template.lastTime }}</span>
              </div>
              <div v-if="template.regularExpressions" class="meta-item">
                <span class="meta-label">正则表达式:</span>
                <span class="meta-value">{{ template.regularExpressions }}</span>
              </div>
            </div>

            <!-- 子项展开/收起 -->
            <div v-if="template.hasChildren && template.children && template.children.length > 0" class="children-section">
              <div class="children-header" @click="toggleChildren(template)">
                <span class="children-title">
                  <i :class="template.expanded ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"></i>
                  会计科目 ({{ template.children.length }})
                </span>
              </div>

              <!-- 子项列表 -->
              <transition name="slide-fade">
                <div v-show="template.expanded" class="children-list">
                  <div
                    v-for="child in template.children"
                    :key="child.id"
                    class="child-item"
                  >
                    <div class="child-content">
                      <div class="child-main">
                        <h4 class="child-name">{{ child.name }}</h4>
                        <p class="child-summary">{{ child.fieldSummary }}</p>
                      </div>
                      <div class="child-details">
                        <div class="detail-row">
                          <span class="detail-label">借贷:</span>
                          <span class="detail-value">{{ child.borrowingAmount }}</span>
                        </div>
                        <div class="detail-row">
                          <span class="detail-label">摘要:</span>
                          <span class="detail-value">{{ child.templAbstract }}</span>
                        </div>
                        <div class="detail-row">
                          <span class="detail-label">排序:</span>
                          <span class="detail-value">{{ child.ordernum }}</span>
                        </div>
                      </div>
                      <div class="child-actions">
                        <el-button type="text" size="mini" @click="editChild(child, template)">
                          编辑
                        </el-button>
                        <el-button type="text" size="mini" @click="deleteChild(child, template)">
                          删除
                        </el-button>
                      </div>
                    </div>
                  </div>

                  <!-- 添加子项按钮 -->
                  <div class="add-child-btn">
                    <el-button type="dashed" size="small" icon="el-icon-plus" @click="addChild(template)">
                      添加会计科目
                    </el-button>
                  </div>
                </div>
              </transition>
            </div>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 添加模板按钮 -->
    <div class="add-template-btn">
      <el-button type="primary" size="medium" icon="el-icon-plus" @click="addTemplate">
        添加新模板
      </el-button>
    </div>
  </section>
</template>
<script>
import { getCertTemplList } from '@/api/system/baseInit'

export default {
  data() {
    return {
      certTemplList: [],
      searchName: '',
      originalList: []
    }
  },
  computed: {

  },
  created() {
    this.loadCertTemplList()
  },
  methods: {
    loadCertTemplList() {
      getCertTemplList(this.searchName).then(res => {
        // 确保每个模板都有 expanded 属性
        const list = (res.list || res || []).map(template => ({
          ...template,
          expanded: false
        }))
        this.certTemplList = list
        this.originalList = [...list]
      })
    },

    handleSearch() {
      this.loadCertTemplList()
    },

    toggleChildren(template) {
      this.$set(template, 'expanded', !template.expanded)
    },

    editTemplate(template) {
      // 编辑模板逻辑
      this.$message.info('编辑模板功能待实现')
      console.log('编辑模板:', template)
    },

    deleteTemplate(template) {
      this.$confirm('确定要删除此模板吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 删除模板逻辑
        this.$message.success('删除成功')
        this.loadCertTemplList()
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    editChild(child, template) {
      // 编辑子项逻辑
      this.$message.info('编辑会计科目功能待实现')
      console.log('编辑子项:', child, '所属模板:', template)
    },

    deleteChild(child, template) {
      this.$confirm('确定要删除此会计科目吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 删除子项逻辑
        this.$message.success('删除成功')
        this.loadCertTemplList()
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    addChild(template) {
      // 添加子项逻辑
      this.$message.info('添加会计科目功能待实现')
      console.log('为模板添加子项:', template)
    },

    addTemplate() {
      // 添加模板逻辑
      this.$message.info('添加新模板功能待实现')
    }
  }
}
</script>
<style scoped>
.cert-template-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.search-bar {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
}

.template-cards-container {
  margin-bottom: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(600px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.template-card {
  width: 100%;
}

.parent-card {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.parent-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.template-info {
  flex: 1;
}

.template-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.template-code,
.template-app {
  margin: 4px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.4;
}

.card-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
  margin-left: 16px;
}

.template-meta {
  margin-bottom: 16px;
}

.meta-item {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.4;
}

.meta-label {
  color: #909399;
  width: 80px;
  flex-shrink: 0;
}

.meta-value {
  color: #606266;
  flex: 1;
  word-break: break-all;
}

.children-section {
  margin-top: 16px;
  border-top: 1px solid #ebeef5;
  padding-top: 16px;
}

.children-header {
  cursor: pointer;
  padding: 8px 0;
  user-select: none;
  transition: color 0.3s ease;
}

.children-header:hover {
  color: #409eff;
}

.children-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  display: flex;
  align-items: center;
}

.children-title i {
  margin-right: 8px;
  transition: transform 0.3s ease;
}

.children-list {
  margin-top: 12px;
}

.child-item {
  background-color: #fafbfc;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.child-item:hover {
  border-color: #c0c4cc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.child-content {
  padding: 16px;
  display: grid;
  grid-template-columns: 1fr 2fr auto;
  gap: 16px;
  align-items: start;
}

.child-main {
  min-width: 0;
}

.child-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  line-height: 1.4;
}

.child-summary {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.4;
  word-break: break-all;
}

.child-details {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.detail-row {
  display: flex;
  font-size: 14px;
  line-height: 1.4;
}

.detail-label {
  color: #909399;
  width: 50px;
  flex-shrink: 0;
}

.detail-value {
  color: #606266;
  flex: 1;
  word-break: break-all;
}

.child-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex-shrink: 0;
}

.add-child-btn {
  text-align: center;
  padding: 16px;
  border-top: 1px dashed #d3d4d6;
  margin-top: 8px;
}

.add-template-btn {
  text-align: center;
  padding: 20px 0;
}

/* 动画效果 */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
}

.slide-fade-enter,
.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cards-grid {
    grid-template-columns: 1fr;
  }

  .child-content {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .child-actions {
    flex-direction: row;
    justify-content: flex-end;
  }

  .card-header {
    flex-direction: column;
    align-items: stretch;
  }

  .card-actions {
    margin-left: 0;
    margin-top: 12px;
    justify-content: flex-end;
  }
}

@media (max-width: 480px) {
  .cert-template-container {
    padding: 12px;
  }

  .search-bar {
    margin-bottom: 16px;
  }

  .search-bar .el-input {
    width: 100% !important;
  }
}
</style>
